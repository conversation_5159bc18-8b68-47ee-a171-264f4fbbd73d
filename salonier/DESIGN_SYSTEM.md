# Sistema de Diseño Salonier - Beauty Tech

## 🎨 Paleta de Colores

### Colores Principales
- **Primary**: `#6B46C1` (Deep Purple) - Color de marca principal
- **Primary Light**: `#8B5CF6` - Variante clara para hover
- **Primary Dark**: `#581C87` - Variante oscura para active
- **Secondary**: `#E8B4B8` (Rose Gold) - Color secundario elegante
- **Secondary Light**: `#F4D0D3` - Variante clara

### Acentos Metálicos
- **Accent Gold**: `#FFD6A5` - Oro rosado sutil
- **Accent Copper**: `#CD7F32` - Cobre claro
- **Accent Pearl**: `#F8F6FB` - Perla suave

### Fondos y Superficies
- **Background**: `#FAFAF9` - <PERSON> cálido
- **Background Secondary**: `#F7F6FB` - Gris lavanda muy claro
- **Card**: `#FFFFFF` - Fondo de cards
- **Card Hover**: `#F8F6FB` - Estado hover para cards

### Colores Semánticos
- **Success**: `#10B981` (Verde esmeralda)
- **Success Light**: `#A7F3D0` (Verde claro)
- **Warning**: `#F59E0B` (Ámbar cálido)
- **Warning Light**: `#FED7AA` (Ámbar claro)
- **Destructive**: `#EF4444` (Rojo coral)
- **Destructive Light**: `#FECACA` (Rojo claro)

## 📝 Tipografía

### Familias de Fuentes
- **Sans**: Inter (Principal) - Texto de cuerpo y UI
- **Display**: Playfair Display - Títulos elegantes y especiales
- **UI**: DM Sans - Elementos de interfaz alternativos
- **Mono**: SF Mono - Números y métricas

### Jerarquía Tipográfica
- **H1**: 36px (2.25rem) - Font weight 700
- **H2**: 30px (1.875rem) - Font weight 600
- **H3**: 24px (1.5rem) - Font weight 600
- **H4**: 20px (1.25rem) - Font weight 600
- **Body**: 16px (1rem) - Line height 1.7
- **Small**: 14px (0.875rem) - Line height 1.6

### Espaciado de Letras
- **Tighter**: -0.03em (Títulos grandes)
- **Tight**: -0.02em (Títulos)
- **Normal**: -0.01em (Texto general)

## 🔲 Espaciado y Layout

### Sistema de Grid 8pt
- **Base**: 8px
- **Espacios**: 4px, 8px, 16px, 24px, 32px, 48px, 64px
- **Márgenes**: Múltiplos de 8px
- **Padding**: Múltiplos de 8px

### Bordes Redondeados
- **SM**: 8px - Elementos pequeños
- **Default**: 12px - Estándar
- **LG**: 16px - Elementos grandes
- **XL**: 24px - Elementos especiales

## 🎭 Efectos Visuales

### Sombras
- **Soft**: Sombra suave para elementos base
- **Medium**: Sombra media para hover
- **Large**: Sombra grande para modales
- **Glow**: Sombra con color para focus

### Glassmorphism
- **Glass**: Fondo semitransparente con blur
- **Glass Dark**: Variante para tema oscuro

### Gradientes
- **Primary**: Linear gradient de primary a primary-light
- **Secondary**: Linear gradient de secondary a secondary-light
- **Accent**: Linear gradient de accent-gold a accent-copper

## 🎬 Animaciones

### Duraciones
- **Fast**: 200ms - Micro-interacciones
- **Normal**: 300ms - Transiciones estándar
- **Slow**: 500ms - Animaciones complejas

### Easing
- **Ease Out**: Para entradas
- **Ease In**: Para salidas
- **Cubic Bezier**: `cubic-bezier(0.4, 0, 0.2, 1)` - Suave

### Animaciones Disponibles
- **fade-in**: Aparición suave
- **slide-up**: Deslizamiento hacia arriba
- **scale-in**: Escalado de entrada
- **pulse-soft**: Pulso suave
- **bounce-gentle**: Rebote suave
- **shimmer**: Efecto de carga
- **stagger-1 a 4**: Animaciones escalonadas

## 🧩 Componentes

### Botones
- **Primary**: Gradiente principal con sombra
- **Secondary**: Color secundario
- **Outline**: Borde con fondo transparente
- **Ghost**: Sin fondo, hover sutil
- **Destructive**: Para acciones peligrosas

### Cards
- **Base**: Fondo blanco, borde sutil, sombra soft
- **Hover**: Elevación con sombra medium
- **Interactive**: Con efectos hover-lift

### Inputs
- **Base**: Borde sutil, focus con glow
- **With Icon**: Padding izquierdo para iconos
- **Error**: Borde rojo con focus destructive

## 📱 Responsive Design

### Breakpoints
- **SM**: 640px - Móviles grandes
- **MD**: 768px - Tablets
- **LG**: 1024px - Laptops
- **XL**: 1280px - Desktops
- **2XL**: 1536px - Pantallas grandes

### Navegación Mobile
- **Bottom Nav**: Navegación inferior en mobile
- **Sidebar**: Overlay en mobile, fijo en desktop
- **Header**: Compacto en mobile, expandido en desktop

## 🎯 Mejores Prácticas

### Accesibilidad
- Contraste mínimo 4.5:1 para texto
- Focus visible en todos los elementos interactivos
- Tamaños de toque mínimo 44px en mobile

### Performance
- Usar `transform` para animaciones
- Evitar animaciones en `width` y `height`
- Usar `will-change` para elementos animados

### Consistencia
- Usar variables CSS para colores
- Mantener espaciado consistente
- Aplicar animaciones uniformes

## 🔧 Implementación

### Variables CSS
Todas las variables están definidas en `globals.css` bajo `:root`

### Clases Tailwind
Configuración extendida en `tailwind.config.ts`

### Componentes
Componentes base en `src/components/ui/`

---

**Salonier Design System v1.0** - Beauty Tech Elegance
