@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Paleta Beauty-Tech Moderna */
  --primary: 107 70 193;          /* Deep Purple - Marca principal */
  --primary-foreground: 255 255 255;
  --primary-light: 139 92 246;    /* Purple más claro para hover */
  --primary-dark: 88 28 135;      /* Purple más oscuro para active */

  --secondary: 232 180 184;       /* Rose <PERSON> suave */
  --secondary-foreground: 26 26 26;
  --secondary-light: 244 208 211; /* Rosa más claro */

  /* Acentos metálicos sutiles */
  --accent-gold: 255 214 165;     /* Oro rosado sutil */
  --accent-copper: 205 127 50;    /* Cobre claro */
  --accent-pearl: 248 246 251;    /* Perla suave */

  /* Fondos y superficies */
  --background: 250 250 249;      /* <PERSON> cálido */
  --background-secondary: 247 246 251; /* Gris lavanda muy claro */
  --foreground: 26 26 26;         /* Charcoal para texto principal */

  /* Colores neutros mejorados */
  --muted: 247 246 251;           /* Gris lavanda suave */
  --muted-foreground: 100 100 106; /* Warm Grey */
  --muted-dark: 156 163 175;      /* Gris medio */

  /* Bordes y elementos de UI */
  --border: 229 229 229;          /* Borde sutil */
  --border-light: 241 245 249;    /* Borde muy claro */
  --input: 255 255 255;           /* Fondo de inputs */
  --ring: 107 70 193;             /* Focus ring */

  /* Colores semánticos mejorados */
  --success: 16 185 129;          /* Verde esmeralda */
  --success-light: 167 243 208;   /* Verde claro */
  --warning: 245 158 11;          /* Ámbar cálido */
  --warning-light: 254 215 170;   /* Ámbar claro */
  --destructive: 239 68 68;       /* Rojo coral */
  --destructive-light: 254 202 202; /* Rojo claro */

  /* Superficies de componentes */
  --card: 255 255 255;            /* Fondo de cards */
  --card-foreground: 26 26 26;
  --card-hover: 248 246 251;      /* Hover state para cards */

  /* Sistema de sombras */
  --shadow-soft: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-glow: 0 0 0 1px rgb(107 70 193 / 0.05), 0 1px 3px 0 rgb(107 70 193 / 0.1);

  /* Bordes redondeados modernos */
  --radius: 0.75rem;              /* 12px - Estándar */
  --radius-sm: 0.5rem;            /* 8px - Pequeño */
  --radius-lg: 1rem;              /* 16px - Grande */
  --radius-xl: 1.5rem;            /* 24px - Extra grande */
}

[data-theme="dark"] {
  /* Tema oscuro beauty-tech */
  --background: 10 10 10;           /* Negro profundo */
  --background-secondary: 17 17 19; /* Gris muy oscuro */
  --foreground: 250 250 249;        /* Blanco cálido */

  --muted: 26 26 26;                /* Gris oscuro */
  --muted-foreground: 160 160 160;  /* Gris medio */
  --muted-dark: 82 82 91;           /* Gris más claro */

  --border: 42 42 42;               /* Borde oscuro */
  --border-light: 39 39 42;         /* Borde más sutil */
  --input: 26 26 26;                /* Fondo de inputs oscuro */

  --card: 26 26 26;                 /* Fondo de cards oscuro */
  --card-foreground: 250 250 249;
  --card-hover: 31 31 35;           /* Hover state para cards */

  /* Acentos metálicos adaptados para tema oscuro */
  --accent-gold: 202 138 4;         /* Oro más intenso */
  --accent-copper: 234 179 8;       /* Cobre dorado */
  --accent-pearl: 39 39 42;         /* Perla oscura */

  /* Sombras adaptadas para tema oscuro */
  --shadow-soft: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-glow: 0 0 0 1px rgb(107 70 193 / 0.2), 0 1px 3px 0 rgb(107 70 193 / 0.3);
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-family: var(--font-inter), 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: tabular-nums;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

/* Jerarquía tipográfica mejorada */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: rgb(var(--foreground));
}

h1 {
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
}

h2 {
  font-size: 1.875rem; /* 30px */
}

h3 {
  font-size: 1.5rem; /* 24px */
}

h4 {
  font-size: 1.25rem; /* 20px */
}

/* Títulos elegantes con Playfair Display */
.font-display {
  font-family: var(--font-playfair), 'Playfair Display', serif;
  font-weight: 700;
  letter-spacing: -0.03em;
}

/* Texto de interfaz con DM Sans */
.font-ui {
  font-family: var(--font-dm-sans), 'DM Sans', sans-serif;
}

/* Mejoras de legibilidad */
p {
  line-height: 1.7;
  color: rgb(var(--foreground));
}

.text-balance {
  text-wrap: balance;
}

/* Números y métricas */
.font-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-numeric: tabular-nums;
}

/* Utilidades de diseño beauty-tech */
.gradient-text {
  background: linear-gradient(135deg, rgb(107 70 193), rgb(232 180 184));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-bg-primary {
  background: linear-gradient(135deg, rgb(107 70 193), rgb(139 92 246));
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, rgb(232 180 184), rgb(244 208 211));
}

.gradient-bg-accent {
  background: linear-gradient(135deg, rgb(255 214 165), rgb(232 180 184));
}

/* Efectos glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sombras personalizadas */
.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-large {
  box-shadow: var(--shadow-large);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

/* Animaciones suaves */
.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estados hover mejorados */
.hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Efectos de enfoque mejorados */
.focus-glow:focus-visible {
  outline: none;
  box-shadow: var(--shadow-glow);
}

/* Animaciones de entrada escalonadas */
.animate-stagger-1 {
  animation: fade-in 0.3s ease-out 0.1s both;
}

.animate-stagger-2 {
  animation: fade-in 0.3s ease-out 0.2s both;
}

.animate-stagger-3 {
  animation: fade-in 0.3s ease-out 0.3s both;
}

.animate-stagger-4 {
  animation: fade-in 0.3s ease-out 0.4s both;
}

/* Efectos de hover para cards */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

/* Efectos de pulso suave */
.pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Efectos de shimmer para loading */
.shimmer {
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Efectos de bounce suave */
.bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

/* Efectos de rotación suave */
.rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Efectos de gradiente animado */
.gradient-animate {
  background: linear-gradient(-45deg,
    rgb(var(--primary)),
    rgb(var(--secondary)),
    rgb(var(--accent-gold)),
    rgb(var(--primary))
  );
  background-size: 400% 400%;
  animation: gradient-animate 15s ease infinite;
}

@keyframes gradient-animate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
