'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/components/ui/toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Mail, Lock, Eye, EyeOff } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const { login, isLoading } = useAuth()
  const { addToast } = useToast()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!email || !password) {
      setError('Por favor completa todos los campos')
      return
    }

    const success = await login(email, password)
    if (success) {
      addToast({
        type: 'success',
        title: '¡Bienvenido!',
        description: 'Has iniciado sesión correctamente'
      })
      router.push('/dashboard')
    } else {
      setError('Credenciales incorrectas')
      addToast({
        type: 'error',
        title: 'Error de autenticación',
        description: 'Verifica tu email y contraseña'
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background-secondary to-accent-pearl flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 animate-fade-in">
        {/* Logo y Header */}
        <div className="text-center space-y-4 animate-slide-up">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center animate-bounce-gentle shadow-large">
            <span className="text-2xl font-bold text-white">S</span>
          </div>
          <div>
            <h1 className="text-4xl font-bold gradient-text font-display">Salonier</h1>
            <p className="text-muted-foreground mt-2 text-lg">
              Plataforma inteligente para coloristas profesionales
            </p>
          </div>
        </div>

        {/* Formulario de Login */}
        <Card className="shadow-large border-0 animate-scale-in glass">
          <CardHeader className="space-y-2 text-center">
            <CardTitle className="text-3xl font-display gradient-text">Iniciar Sesión</CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Ingresa tus credenciales para acceder a Salonier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="Email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="h-4 w-4" />}
                error={error && !email ? 'Email requerido' : ''}
              />
              
              <div className="relative">
                <Input
                  label="Contraseña"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  icon={<Lock className="h-4 w-4" />}
                  error={error && !password ? 'Contraseña requerida' : ''}
                />
                <button
                  type="button"
                  className="absolute right-3 top-9 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>

              {error && (
                <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20">
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                size="lg"
                isLoading={isLoading}
              >
                {isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-muted/50 rounded-lg">
              <p className="text-sm font-medium text-muted-foreground mb-2">
                Credenciales de demo:
              </p>
              <div className="space-y-1 text-sm">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Contraseña:</strong> demo123</p>
              </div>
              <div className="mt-2 space-y-1 text-sm">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Contraseña:</strong> demo123</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>© 2025 Salonier. Transformando la industria del color.</p>
        </div>
      </div>
    </div>
  )
}
