'use client'

import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { MetricCard } from '@/components/ui/metric-card'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  DollarSign, 
  Users, 
  Palette, 
  TrendingUp,
  Plus,
  Calendar,
  Clock,
  Star
} from 'lucide-react'
import { mockBusinessMetrics, mockClients, mockRecentServices, getUserById } from '@/data/mockData'
import { formatCurrency, timeAgo } from '@/lib/utils'

export default function DashboardPage() {
  const metrics = mockBusinessMetrics
  const recentClients = mockClients.slice(0, 5)
  const recentServices = mockRecentServices

  return (
    <DashboardLayout 
      title="Dashboard" 
      subtitle="Resumen de tu actividad y métricas clave"
    >
      <div className="space-y-6">
        {/* Métricas principales */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <MetricCard
            title="Ingresos del Mes"
            value={formatCurrency(metrics.revenue)}
            change={{ value: 12.5, type: 'increase', period: 'mes anterior' }}
            icon={DollarSign}
          />
          <MetricCard
            title="Servicios Realizados"
            value={metrics.services}
            change={{ value: 8.2, type: 'increase', period: 'mes anterior' }}
            icon={Palette}
          />
          <MetricCard
            title="Ticket Promedio"
            value={formatCurrency(metrics.averageTicket)}
            change={{ value: 5.1, type: 'increase', period: 'mes anterior' }}
            icon={TrendingUp}
          />
          <MetricCard
            title="Retención de Clientes"
            value={`${metrics.clientRetention}%`}
            change={{ value: 2.3, type: 'increase', period: 'mes anterior' }}
            icon={Users}
          />
        </div>

        {/* Acciones rápidas */}
        <Card>
          <CardHeader>
            <CardTitle>Acciones Rápidas</CardTitle>
            <CardDescription>
              Accede rápidamente a las funciones más utilizadas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Botón de Pánico - Corrección Express */}
              <div className="bg-gradient-to-r from-red-500 to-orange-500 rounded-xl p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-bold text-lg">¿Problema con el Color?</h3>
                    <p className="text-red-100 text-sm">Corrección express en 3 pasos</p>
                  </div>
                  <Button
                    size="lg"
                    className="bg-white text-red-600 hover:bg-red-50 font-bold px-6"
                  >
                    🚨 CORREGIR AHORA
                  </Button>
                </div>
              </div>

              {/* Acciones normales */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Button className="h-16 sm:h-20 flex-col space-y-1 sm:space-y-2" size="lg">
                  <Plus className="h-5 w-5 sm:h-6 sm:w-6" />
                  <span className="text-sm sm:text-base">Nuevo Servicio</span>
                </Button>
                <Button variant="outline" className="h-16 sm:h-20 flex-col space-y-1 sm:space-y-2" size="lg">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6" />
                  <span className="text-sm sm:text-base">Agregar Cliente</span>
                </Button>
                <Button variant="outline" className="h-16 sm:h-20 flex-col space-y-1 sm:space-y-2" size="lg">
                  <Palette className="h-5 w-5 sm:h-6 sm:w-6" />
                  <span className="text-sm sm:text-base">Crear Fórmula</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Servicios recientes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Servicios Recientes
              </CardTitle>
              <CardDescription>
                Últimos servicios realizados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentServices.map((service) => {
                  const client = mockClients.find(c => c.id === service.clientId)
                  const stylist = getUserById(service.performedBy)
                  
                  return (
                    <div key={service.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-white">
                            {client?.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-foreground">{client?.name}</p>
                          <p className="text-sm text-muted-foreground">
                            por {stylist?.name} • {timeAgo(service.scheduledAt)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-foreground">
                          {formatCurrency(service.totalCharged)}
                        </p>
                        {service.clientRating && (
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-muted-foreground ml-1">
                              {service.clientRating}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Clientes recientes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Clientes Recientes
              </CardTitle>
              <CardDescription>
                Últimos clientes agregados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentClients.map((client) => (
                  <div key={client.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {client.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-foreground">{client.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {client.vipStatus && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                              VIP
                            </span>
                          )}
                          {client.lastVisit ? timeAgo(client.lastVisit) : 'Nuevo cliente'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Button variant="ghost" size="sm">
                        Ver perfil
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Próximas citas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Próximas Citas
            </CardTitle>
            <CardDescription>
              Agenda para hoy y mañana
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No hay citas programadas</p>
              <Button variant="outline" className="mt-4">
                Programar Cita
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
