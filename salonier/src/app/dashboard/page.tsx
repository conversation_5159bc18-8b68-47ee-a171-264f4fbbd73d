'use client'

import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { MetricCard } from '@/components/ui/metric-card'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  DollarSign, 
  Users, 
  Palette, 
  TrendingUp,
  Plus,
  Calendar,
  Clock,
  Star
} from 'lucide-react'
import { mockBusinessMetrics, mockClients, mockRecentServices, getUserById } from '@/data/mockData'
import { formatCurrency, timeAgo } from '@/lib/utils'

export default function DashboardPage() {
  const metrics = mockBusinessMetrics
  const recentClients = mockClients.slice(0, 5)
  const recentServices = mockRecentServices

  return (
    <DashboardLayout
      title="Dashboard"
      subtitle="Resumen de tu actividad y métricas clave"
    >
      {/* Métricas principales */}
      <section className="animate-fade-in">
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          <MetricCard
            title="Ingresos del Mes"
            value={formatCurrency(metrics.revenue)}
            change={{ value: 12.5, type: 'increase', period: 'mes anterior' }}
            icon={DollarSign}
            className="animate-stagger-1"
          />
          <MetricCard
            title="Servicios Realizados"
            value={metrics.services}
            change={{ value: 8.2, type: 'increase', period: 'mes anterior' }}
            icon={Palette}
            className="animate-stagger-2"
          />
          <MetricCard
            title="Ticket Promedio"
            value={formatCurrency(metrics.averageTicket)}
            change={{ value: 5.1, type: 'increase', period: 'mes anterior' }}
            icon={TrendingUp}
            className="animate-stagger-3"
          />
          <MetricCard
            title="Retención de Clientes"
            value={`${metrics.clientRetention}%`}
            change={{ value: 2.3, type: 'increase', period: 'mes anterior' }}
            icon={Users}
            className="animate-stagger-4"
          />
        </div>
      </section>

      {/* Acciones rápidas */}
      <section className="animate-slide-up">
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-background-secondary to-accent-pearl/30">
            <CardTitle className="font-display text-2xl">Acciones Rápidas</CardTitle>
            <CardDescription className="text-base">
              Accede rápidamente a las funciones más utilizadas
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              {/* Botón de Pánico - Corrección Express */}
              <div className="bg-gradient-to-r from-destructive to-warning rounded-xl p-6 text-white shadow-large hover:shadow-glow transition-all duration-300 hover:scale-[1.02]">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-bold text-xl font-display">¿Problema con el Color?</h3>
                    <p className="text-white/90 text-sm mt-1">Corrección express en 3 pasos</p>
                  </div>
                  <Button
                    size="lg"
                    className="bg-white text-destructive hover:bg-white/90 font-bold px-8 shadow-medium hover:shadow-large"
                  >
                    🚨 CORREGIR AHORA
                  </Button>
                </div>
              </div>

              {/* Acciones normales */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                <Button className="h-18 sm:h-20 lg:h-24 flex-col space-y-2 font-ui text-center" size="lg">
                  <Plus className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7" />
                  <span className="text-xs sm:text-sm lg:text-base font-medium">Nuevo Servicio</span>
                </Button>
                <Button variant="outline" className="h-18 sm:h-20 lg:h-24 flex-col space-y-2 font-ui text-center" size="lg">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7" />
                  <span className="text-xs sm:text-sm lg:text-base font-medium">Agregar Cliente</span>
                </Button>
                <Button variant="outline" className="h-18 sm:h-20 lg:h-24 flex-col space-y-2 font-ui text-center" size="lg">
                  <Palette className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7" />
                  <span className="text-xs sm:text-sm lg:text-base font-medium">Crear Fórmula</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Servicios recientes */}
        <Card className="animate-slide-up">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center font-display text-xl">
              <Clock className="h-5 w-5 mr-3 text-primary" />
              Servicios Recientes
            </CardTitle>
            <CardDescription className="text-base">
              Últimos servicios realizados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentServices.map((service) => {
                const client = mockClients.find(c => c.id === service.clientId)
                const stylist = getUserById(service.performedBy)

                return (
                  <div key={service.id} className="flex items-center justify-between p-4 rounded-xl bg-background-secondary/30 hover:bg-background-secondary/50 transition-all duration-200 hover:shadow-soft">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-soft">
                        <span className="text-sm font-medium text-white">
                          {client?.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-semibold text-foreground font-ui">{client?.name}</p>
                        <p className="text-sm text-muted-foreground">
                          por {stylist?.name} • {timeAgo(service.scheduledAt)}
                        </p>
                      </div>
                    </div>
                      <div className="text-right">
                        <p className="font-medium text-foreground">
                          {formatCurrency(service.totalCharged)}
                        </p>
                        {service.clientRating && (
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-muted-foreground ml-1">
                              {service.clientRating}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Clientes recientes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Clientes Recientes
              </CardTitle>
              <CardDescription>
                Últimos clientes agregados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentClients.map((client) => (
                  <div key={client.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {client.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-foreground">{client.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {client.vipStatus && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                              VIP
                            </span>
                          )}
                          {client.lastVisit ? timeAgo(client.lastVisit) : 'Nuevo cliente'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Button variant="ghost" size="sm">
                        Ver perfil
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Próximas citas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Próximas Citas
            </CardTitle>
            <CardDescription>
              Agenda para hoy y mañana
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No hay citas programadas</p>
              <Button variant="outline" className="mt-4">
                Programar Cita
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
