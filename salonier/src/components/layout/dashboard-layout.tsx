'use client'

import { useRequireAuth } from '@/lib/auth-context'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { BottomNav } from './bottom-nav'
import { LoadingScreen } from '@/components/ui/loading'

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  subtitle?: string
}

export function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) {
    return <LoadingScreen message="Verificando autenticación..." />
  }

  if (!user) {
    return null // useRequireAuth will redirect to login
  }

  return (
    <div className="min-h-screen bg-background">
      <Sidebar />

      <div className="lg:pl-64">
        <Header title={title} subtitle={subtitle} />

        <main className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto pb-20 lg:pb-8">
          <div className="space-y-6 lg:space-y-8">
            {children}
          </div>
        </main>
      </div>

      <BottomNav />
    </div>
  )
}
