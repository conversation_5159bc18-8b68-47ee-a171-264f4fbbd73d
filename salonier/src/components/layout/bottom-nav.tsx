'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { 
  Home, 
  Users, 
  Palette, 
  BarChart3, 
  Plus 
} from 'lucide-react'

const navigation = [
  { name: 'Inicio', href: '/dashboard', icon: Home },
  { name: 'Clientes', href: '/clients', icon: Users },
  { name: 'Nuevo', href: '/formulas', icon: Plus, isSpecial: true },
  { name: 'Fórmulas', href: '/formulas', icon: Palette },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
]

export function BottomNav() {
  const pathname = usePathname()

  return (
    <nav className="lg:hidden fixed bottom-0 left-0 right-0 z-40 glass border-t border-border-light">
      <div className="grid grid-cols-5 h-16">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          const isSpecial = item.isSpecial
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center space-y-1 transition-all duration-200",
                isSpecial && "relative -top-2",
                isActive && !isSpecial && "text-primary",
                !isActive && !isSpecial && "text-muted-foreground hover:text-foreground"
              )}
            >
              {isSpecial ? (
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center shadow-large hover:shadow-glow transition-all duration-200 hover:scale-110">
                  <item.icon className="h-6 w-6 text-white" />
                </div>
              ) : (
                <item.icon className={cn(
                  "h-5 w-5 transition-all duration-200",
                  isActive && "scale-110"
                )} />
              )}
              
              {!isSpecial && (
                <span className="text-xs font-medium font-ui">
                  {item.name}
                </span>
              )}
              
              {isActive && !isSpecial && (
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse-soft" />
              )}
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
