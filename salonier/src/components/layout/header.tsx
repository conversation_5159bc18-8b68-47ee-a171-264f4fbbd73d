'use client'

import { Bell, Search, Sun, Moon } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/lib/auth-context'

interface HeaderProps {
  title: string
  subtitle?: string
}

export function Header({ title, subtitle }: HeaderProps) {
  const { user, organization } = useAuth()

  return (
    <header className="glass border-b border-border-light sticky top-0 z-30 animate-fade-in">
      <div className="px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Title Section */}
          <div className="flex-1 min-w-0 lg:pl-0 pl-12">
            <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">{title}</h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground mt-1 hidden sm:block">{subtitle}</p>
            )}
          </div>

          {/* Actions Section */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Search */}
            <div className="hidden lg:block">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar clientes, servicios..."
                  className="pl-10 w-48 xl:w-64 bg-background-secondary/50 border-border-light focus:bg-background"
                />
              </div>
            </div>

            {/* Mobile Search Button */}
            <Button variant="ghost" size="sm" className="lg:hidden hover:bg-background-secondary/50">
              <Search className="h-5 w-5" />
            </Button>

            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative hover:bg-background-secondary/50">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white animate-pulse-soft shadow-soft">
                3
              </span>
            </Button>

            {/* Theme Toggle - Hidden on mobile */}
            <Button variant="ghost" size="sm" className="hidden sm:flex hover:bg-background-secondary/50">
              <Sun className="h-5 w-5" />
            </Button>

            {/* User Menu */}
            <div className="flex items-center space-x-3 pl-2 sm:pl-4 border-l border-border">
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium text-foreground">{user?.name}</p>
                <p className="text-xs text-muted-foreground">{organization?.name}</p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-soft hover:shadow-medium">
                <span className="text-sm font-medium text-white">
                  {user?.name.charAt(0)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
