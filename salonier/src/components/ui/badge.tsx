import * as React from "react"
import { cn } from "@/lib/utils"

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'destructive' | 'outline' | 'accent'
  size?: 'sm' | 'md' | 'lg'
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', ...props }, ref) => {
    const baseClasses = "inline-flex items-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
    
    const variants = {
      default: "bg-primary text-primary-foreground shadow-soft hover:shadow-medium",
      secondary: "bg-secondary text-secondary-foreground shadow-soft hover:shadow-medium",
      success: "bg-success-light/20 text-success border border-success/20 hover:bg-success-light/30",
      warning: "bg-warning-light/20 text-warning border border-warning/20 hover:bg-warning-light/30",
      destructive: "bg-destructive-light/20 text-destructive border border-destructive/20 hover:bg-destructive-light/30",
      outline: "border border-border bg-background hover:bg-muted text-foreground",
      accent: "bg-gradient-to-r from-accent-gold/20 to-accent-copper/20 text-accent-copper border border-accent-copper/20 hover:from-accent-gold/30 hover:to-accent-copper/30"
    }
    
    const sizes = {
      sm: "px-2 py-1 text-xs rounded-md",
      md: "px-3 py-1.5 text-sm rounded-lg",
      lg: "px-4 py-2 text-base rounded-xl"
    }

    return (
      <div
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Badge.displayName = "Badge"

export { Badge }
