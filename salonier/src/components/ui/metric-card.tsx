import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface MetricCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: LucideIcon
  className?: string
}

export function MetricCard({ title, value, change, icon: Icon, className }: MetricCardProps) {
  return (
    <Card className={cn("group animate-fade-in", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-muted-foreground group-hover:text-muted-dark transition-colors">{title}</p>
            <p className="text-2xl font-bold text-foreground mt-2 group-hover:text-primary transition-colors">{value}</p>
            {change && (
              <div className="flex items-center mt-2">
                <span className={cn(
                  "text-sm font-medium px-2 py-1 rounded-full",
                  change.type === 'increase'
                    ? "text-success bg-success-light/20"
                    : "text-destructive bg-destructive-light/20"
                )}>
                  {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
                </span>
                <span className="text-sm text-muted-foreground ml-2">
                  vs {change.period}
                </span>
              </div>
            )}
          </div>
          <div className="ml-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300 group-hover:scale-110">
              <Icon className="h-6 w-6 text-primary group-hover:text-primary-dark transition-colors" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
