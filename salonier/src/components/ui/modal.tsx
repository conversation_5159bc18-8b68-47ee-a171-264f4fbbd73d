'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { X } from "lucide-react"

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function Modal({ 
  isOpen, 
  onClose, 
  title, 
  description, 
  children, 
  size = 'md',
  className 
}: ModalProps) {
  // Cerrar modal con Escape
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose()
    }
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop con glassmorphism */}
      <div 
        className="absolute inset-0 bg-black/20 backdrop-blur-sm animate-fade-in"
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div className={cn(
        "relative w-full glass rounded-xl shadow-large animate-scale-in",
        sizeClasses[size],
        className
      )}>
        {/* Header */}
        {(title || description) && (
          <div className="flex items-start justify-between p-6 border-b border-border-light">
            <div className="flex-1">
              {title && (
                <h2 className="text-xl font-semibold text-foreground text-balance">
                  {title}
                </h2>
              )}
              {description && (
                <p className="text-sm text-muted-foreground mt-1">
                  {description}
                </p>
              )}
            </div>
            <button
              onClick={onClose}
              className="ml-4 p-2 rounded-lg hover:bg-muted transition-colors focus-glow"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}
        
        {/* Content */}
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  )
}

// Componente para el contenido del modal
export function ModalContent({ children, className }: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {children}
    </div>
  )
}

// Componente para los botones del modal
export function ModalActions({ children, className }: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={cn(
      "flex items-center justify-end space-x-3 pt-4 border-t border-border-light",
      className
    )}>
      {children}
    </div>
  )
}
