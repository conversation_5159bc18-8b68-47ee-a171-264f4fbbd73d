import * as React from "react"
import { cn } from "@/lib/utils"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  isLoading?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center rounded-xl font-medium transition-all duration-300 focus-visible:outline-none focus-glow disabled:pointer-events-none disabled:opacity-50 hover-lift"

    const variants = {
      primary: "bg-gradient-to-r from-primary to-primary-light text-primary-foreground shadow-medium hover:shadow-large hover:scale-[1.02] active:scale-[0.98] hover:from-primary-dark hover:to-primary",
      secondary: "bg-secondary text-secondary-foreground shadow-soft hover:bg-secondary-light hover:shadow-medium",
      outline: "border border-border bg-background hover:bg-muted hover:text-muted-foreground hover:border-primary/20",
      ghost: "hover:bg-muted hover:text-muted-foreground hover:shadow-soft",
      destructive: "bg-destructive text-white shadow-soft hover:bg-destructive/90 hover:shadow-medium"
    }
    
    const sizes = {
      sm: "h-9 px-3 text-sm",
      md: "h-11 px-6 text-sm",
      lg: "h-12 px-8 text-base"
    }

    return (
      <button
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </button>
    )
  }
)
Button.displayName = "Button"

export { Button }
