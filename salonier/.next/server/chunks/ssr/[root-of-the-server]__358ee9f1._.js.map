{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-300 focus-visible:outline-none focus-glow disabled:pointer-events-none disabled:opacity-50 hover-lift\"\n\n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-primary-light text-primary-foreground shadow-medium hover:shadow-large hover:scale-[1.02] active:scale-[0.98] hover:from-primary-dark hover:to-primary\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-soft hover:bg-secondary-light hover:shadow-medium\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground hover:border-primary/20\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground hover:shadow-soft\",\n      destructive: \"bg-destructive text-white shadow-soft hover:bg-destructive/90 hover:shadow-medium\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport {\n  Home,\n  Users,\n  Palette,\n  Package,\n  BarChart3,\n  Timer,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  Zap\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Conversión', href: '/conversion', icon: Zap },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-white/90 backdrop-blur-sm shadow-lg border-gray-200\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card/95 backdrop-blur-sm border-r border-border-light shadow-large transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-5 border-b border-border-light\">\n            <div className=\"w-9 h-9 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text font-display\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-5 border-b border-border-light bg-background-secondary/30\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-11 h-11 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-semibold text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate font-medium\">\n                  {user?.role === 'owner' ? 'Propietario' :\n                   user?.role === 'colorist' ? 'Colorista' :\n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 hover-lift\",\n                    isActive\n                      ? \"bg-gradient-to-r from-primary to-primary-light text-primary-foreground shadow-medium\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-soft\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-all duration-200\",\n                    isActive\n                      ? \"text-primary-foreground\"\n                      : \"text-muted-foreground group-hover:text-foreground group-hover:scale-110\"\n                  )} />\n                  <span className=\"font-ui\">{item.name}</span>\n                  {isActive && (\n                    <div className=\"ml-auto w-2 h-2 bg-white rounded-full animate-pulse\" />\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border-light bg-background-secondary/20\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-destructive hover:bg-destructive-light/10 font-ui\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AApBA;;;;;;;;;AAsBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gMAAA,CAAA,MAAG;IAAC;IACrD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kMACA,SAAS,kBAAkB;0BAE3B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAoD;;;;;;;;;;;;sCAItE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WACI,yFACA;;sDAGN,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,4BACA;;;;;;sDAEN,8OAAC;4CAAK,WAAU;sDAAW,KAAK,IAAI;;;;;;wCACnC,0BACC,8OAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-glow hover:border-primary/20 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:shadow-glow\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gSACA,QAAQ,SACR,SAAS,gDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,8OAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Sun, Moon } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"glass border-b border-border-light sticky top-0 z-30 animate-fade-in\">\n      <div className=\"px-4 sm:px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1 min-w-0 lg:pl-0 pl-12\">\n            <h1 className=\"text-xl sm:text-2xl font-bold text-foreground truncate\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground mt-1 hidden sm:block\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            {/* Search */}\n            <div className=\"hidden lg:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-48 xl:w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Mobile Search Button */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"lg:hidden\">\n              <Search className=\"h-5 w-5\" />\n            </Button>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle - Hidden on mobile */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"hidden sm:flex\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-2 sm:pl-4 border-l border-border\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-foreground\">{user?.name}</p>\n                <p className=\"text-xs text-muted-foreground\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-soft hover:shadow-medium\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAErC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0D;;;;;;4BACvE,0BACC,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;kCAKvE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA+G;;;;;;;;;;;;0CAMjI,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuC,MAAM;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAiC,cAAc;;;;;;;;;;;;kDAE9D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className={cn('animate-spin', sizeClasses[size], className)}>\n      <svg\n        className=\"h-full w-full\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n      >\n        <circle\n          className=\"opacity-25\"\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"4\"\n        />\n        <path\n          className=\"opacity-75\"\n          fill=\"currentColor\"\n          d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        />\n      </svg>\n    </div>\n  )\n}\n\ninterface LoadingScreenProps {\n  message?: string\n}\n\nexport function LoadingScreen({ message = 'Cargando...' }: LoadingScreenProps) {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 flex items-center justify-center\">\n      <div className=\"text-center space-y-6 animate-fade-in\">\n        <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-400 rounded-2xl flex items-center justify-center animate-bounce-gentle\">\n          <span className=\"text-3xl font-bold text-white\">S</span>\n        </div>\n        <div className=\"space-y-2\">\n          <h1 className=\"text-4xl font-bold gradient-text\">Salonier</h1>\n          <p className=\"text-gray-600\">{message}</p>\n        </div>\n        <LoadingSpinner size=\"lg\" className=\"mx-auto text-purple-600\" />\n      </div>\n    </div>\n  )\n}\n\ninterface SkeletonProps {\n  className?: string\n}\n\nexport function Skeleton({ className }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        'animate-pulse rounded-md bg-gray-200',\n        className\n      )}\n    />\n  )\n}\n\nexport function CardSkeleton() {\n  return (\n    <div className=\"p-6 border rounded-xl bg-white shadow-sm\">\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center space-x-4\">\n          <Skeleton className=\"h-12 w-12 rounded-full\" />\n          <div className=\"space-y-2 flex-1\">\n            <Skeleton className=\"h-4 w-3/4\" />\n            <Skeleton className=\"h-3 w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-3 w-full\" />\n          <Skeleton className=\"h-3 w-5/6\" />\n        </div>\n        <div className=\"flex space-x-2\">\n          <Skeleton className=\"h-8 w-20\" />\n          <Skeleton className=\"h-8 w-24\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function TableSkeleton({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"space-y-4\">\n      {Array.from({ length: rows }).map((_, i) => (\n        <div key={i} className=\"flex items-center space-x-4 p-4 border rounded-lg\">\n          <Skeleton className=\"h-10 w-10 rounded-full\" />\n          <div className=\"flex-1 space-y-2\">\n            <Skeleton className=\"h-4 w-1/3\" />\n            <Skeleton className=\"h-3 w-1/4\" />\n          </div>\n          <Skeleton className=\"h-4 w-20\" />\n          <Skeleton className=\"h-4 w-16\" />\n          <Skeleton className=\"h-8 w-24\" />\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,WAAW,CAAC,KAAK,EAAE;kBACpD,cAAA,8OAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;;;;;;AAKZ;AAMO,SAAS,cAAc,EAAE,UAAU,aAAa,EAAsB;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;8BAElD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAEhC,8OAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;;AAI5C;AAMO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA;;;;;;AAIR;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAS,WAAU;;;;;;8CACpB,8OAAC;oCAAS,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;8BAEtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS,cAAc,EAAE,OAAO,CAAC,EAAqB;IAC3D,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,WAAU;;;;;;0CACpB,8OAAC;gCAAS,WAAU;;;;;;;;;;;;kCAEtB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;;eARZ;;;;;;;;;;AAalB", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return <LoadingScreen message=\"Verificando autenticación...\" />\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n\n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n\n        <main className=\"p-6 lg:p-8 max-w-7xl mx-auto\">\n          <div className=\"space-y-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBAAO,8OAAC,mIAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAER,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft hover:shadow-medium hover:bg-card-hover transition-all duration-300 hover-lift\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/metric-card.tsx"], "sourcesContent": ["import { Card, CardContent } from '@/components/ui/card'\nimport { cn } from '@/lib/utils'\nimport { LucideIcon } from 'lucide-react'\n\ninterface MetricCardProps {\n  title: string\n  value: string | number\n  change?: {\n    value: number\n    type: 'increase' | 'decrease'\n    period: string\n  }\n  icon: LucideIcon\n  className?: string\n}\n\nexport function MetricCard({ title, value, change, icon: Icon, className }: MetricCardProps) {\n  return (\n    <Card className={cn(\"group animate-fade-in\", className)}>\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-muted-foreground group-hover:text-muted-dark transition-colors\">{title}</p>\n            <p className=\"text-2xl font-bold text-foreground mt-2 group-hover:text-primary transition-colors\">{value}</p>\n            {change && (\n              <div className=\"flex items-center mt-2\">\n                <span className={cn(\n                  \"text-sm font-medium px-2 py-1 rounded-full\",\n                  change.type === 'increase'\n                    ? \"text-success bg-success-light/20\"\n                    : \"text-destructive bg-destructive-light/20\"\n                )}>\n                  {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%\n                </span>\n                <span className=\"text-sm text-muted-foreground ml-2\">\n                  vs {change.period}\n                </span>\n              </div>\n            )}\n          </div>\n          <div className=\"ml-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300 group-hover:scale-110\">\n              <Icon className=\"h-6 w-6 text-primary group-hover:text-primary-dark transition-colors\" />\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAmB;IACzF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAC3C,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA2F;;;;;;0CACxG,8OAAC;gCAAE,WAAU;0CAAsF;;;;;;4BAClG,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,OAAO,IAAI,KAAK,aACZ,qCACA;;4CAEH,OAAO,IAAI,KAAK,aAAa,MAAM;4CAAK,KAAK,GAAG,CAAC,OAAO,KAAK;4CAAE;;;;;;;kDAElE,8OAAC;wCAAK,WAAU;;4CAAqC;4CAC/C,OAAO,MAAM;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B", "debugId": null}}]}