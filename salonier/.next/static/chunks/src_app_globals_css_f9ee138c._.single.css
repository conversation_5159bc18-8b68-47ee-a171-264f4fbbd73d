/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-duration: initial;
    }
  }
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.top-1\/2 {
  top: 50%;
}

.top-full {
  top: 100%;
}

.right-full {
  right: 100%;
}

.bottom-full {
  bottom: 100%;
}

.left-1\/2 {
  left: 50%;
}

.left-full {
  left: 100%;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.mx-auto {
  margin-inline: auto;
}

.ml-auto {
  margin-left: auto;
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.inline-block {
  display: inline-block;
}

.inline-flex {
  display: inline-flex;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.3333%;
}

.w-1\/4 {
  width: 25%;
}

.w-3\/4 {
  width: 75%;
}

.w-5\/6 {
  width: 83.3333%;
}

.w-fit {
  width: fit-content;
}

.w-full {
  width: 100%;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.-translate-x-1\/2 {
  --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.translate-x-full {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.scale-110 {
  --tw-scale-x: 110%;
  --tw-scale-y: 110%;
  --tw-scale-z: 110%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}

.transform {
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.cursor-pointer {
  cursor: pointer;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-0 {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}

.border-4 {
  border-style: var(--tw-border-style);
  border-width: 4px;
}

.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}

.border-r {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}

.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.border-l {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}

.border-l-4 {
  border-left-style: var(--tw-border-style);
  border-left-width: 4px;
}

.border-dashed {
  --tw-border-style: dashed;
  border-style: dashed;
}

.border-t-transparent {
  border-top-color: #0000;
}

.border-r-transparent {
  border-right-color: #0000;
}

.border-b-transparent {
  border-bottom-color: #0000;
}

.border-l-transparent {
  border-left-color: #0000;
}

.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.fill-current {
  fill: currentColor;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.leading-none {
  --tw-leading: 1;
  line-height: 1;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.italic {
  font-style: italic;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-0 {
  opacity: 0;
}

.opacity-25 {
  opacity: .25;
}

.opacity-50 {
  opacity: .5;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-90 {
  opacity: .9;
}

.opacity-100 {
  opacity: 1;
}

.ring {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}

.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.duration-200 {
  --tw-duration: .2s;
  transition-duration: .2s;
}

.duration-300 {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.duration-1000 {
  --tw-duration: 1s;
  transition-duration: 1s;
}

@media (hover: hover) {
  .group-hover\:scale-110:is(:where(.group):hover *) {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

.file\:border-0::file-selector-button {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

@media (hover: hover) {
  .hover\:scale-105:hover {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (hover: hover) {
  .hover\:scale-110:hover {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (hover: hover) {
  .hover\:scale-\[1\.02\]:hover {
    scale: 1.02;
  }
}

@media (hover: hover) {
  .hover\:opacity-100:hover {
    opacity: 1;
  }
}

.focus\:border-transparent:focus {
  border-color: #0000;
}

.focus\:ring-2:focus {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}

.focus\:outline-none:focus {
  --tw-outline-style: none;
  outline-style: none;
}

.focus-visible\:outline-none:focus-visible {
  --tw-outline-style: none;
  outline-style: none;
}

.active\:scale-\[0\.98\]:active {
  scale: .98;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

:root {
  --primary: 107 70 193;
  --primary-foreground: 255 255 255;
  --primary-light: 139 92 246;
  --primary-dark: 88 28 135;
  --secondary: 232 180 184;
  --secondary-foreground: 26 26 26;
  --secondary-light: 244 208 211;
  --accent-gold: 255 214 165;
  --accent-copper: 205 127 50;
  --accent-pearl: 248 246 251;
  --background: 250 250 249;
  --background-secondary: 247 246 251;
  --foreground: 26 26 26;
  --muted: 247 246 251;
  --muted-foreground: 100 100 106;
  --muted-dark: 156 163 175;
  --border: 229 229 229;
  --border-light: 241 245 249;
  --input: 255 255 255;
  --ring: 107 70 193;
  --success: 16 185 129;
  --success-light: 167 243 208;
  --warning: 245 158 11;
  --warning-light: 254 215 170;
  --destructive: 239 68 68;
  --destructive-light: 254 202 202;
  --card: 255 255 255;
  --card-foreground: 26 26 26;
  --card-hover: 248 246 251;
  --shadow-soft: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
  --shadow-medium: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --shadow-large: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --shadow-glow: 0 0 0 1px #6b46c10d, 0 1px 3px 0 #6b46c11a;
  --radius: .75rem;
  --radius-sm: .5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
}

[data-theme="dark"] {
  --background: 10 10 10;
  --background-secondary: 17 17 19;
  --foreground: 250 250 249;
  --muted: 26 26 26;
  --muted-foreground: 160 160 160;
  --muted-dark: 82 82 91;
  --border: 42 42 42;
  --border-light: 39 39 42;
  --input: 26 26 26;
  --card: 26 26 26;
  --card-foreground: 250 250 249;
  --card-hover: 31 31 35;
  --accent-gold: 202 138 4;
  --accent-copper: 234 179 8;
  --accent-pearl: 39 39 42;
  --shadow-soft: 0 1px 3px 0 #0000004d, 0 1px 2px -1px #0000004d;
  --shadow-medium: 0 4px 6px -1px #0000004d, 0 2px 4px -2px #0000004d;
  --shadow-large: 0 10px 15px -3px #0000004d, 0 4px 6px -4px #0000004d;
  --shadow-glow: 0 0 0 1px #6b46c133, 0 1px 3px 0 #6b46c14d;
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-family: var(--font-inter), "Inter", system-ui, sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  font-variant-numeric: tabular-nums;
  letter-spacing: -.01em;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  letter-spacing: -.02em;
  color: rgb(var(--foreground));
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

.font-display {
  font-family: var(--font-playfair), "Playfair Display", serif;
  letter-spacing: -.03em;
  font-weight: 700;
}

.font-ui {
  font-family: var(--font-dm-sans), "DM Sans", sans-serif;
}

p {
  color: rgb(var(--foreground));
  line-height: 1.7;
}

.text-balance {
  text-wrap: balance;
}

.font-mono {
  font-variant-numeric: tabular-nums;
  font-family: SF Mono, Monaco, Inconsolata, Roboto Mono, monospace;
}

.gradient-text {
  background: linear-gradient(135deg, #6b46c1, #e8b4b8);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.gradient-bg-primary {
  background: linear-gradient(135deg, #6b46c1, #8b5cf6);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #e8b4b8, #f4d0d3);
}

.gradient-bg-accent {
  background: linear-gradient(135deg, #ffd6a5, #e8b4b8);
}

.glass {
  -webkit-backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
}

.glass-dark {
  -webkit-backdrop-filter: blur(10px);
  background: #0000001a;
  border: 1px solid #ffffff1a;
}

.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-large {
  box-shadow: var(--shadow-large);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.animate-scale-in {
  animation: .2s ease-out scale-in;
}

.animate-fade-in {
  animation: .3s ease-out fade-in;
}

.animate-slide-up {
  animation: .3s ease-out slide-up;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hover-lift {
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
}

.hover-lift:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.focus-glow:focus-visible {
  box-shadow: var(--shadow-glow);
  outline: none;
}

.animate-stagger-1 {
  animation: .3s ease-out .1s both fade-in;
}

.animate-stagger-2 {
  animation: .3s ease-out .2s both fade-in;
}

.animate-stagger-3 {
  animation: .3s ease-out .3s both fade-in;
}

.animate-stagger-4 {
  animation: .3s ease-out .4s both fade-in;
}

.card-hover {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.card-hover:hover {
  box-shadow: var(--shadow-large);
  transform: translateY(-4px);
}

.pulse-soft {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse-soft;
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.shimmer {
  background: linear-gradient(90deg, #0000, #fff6, #0000) 0 0 / 200% 100%;
  animation: 1.5s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.bounce-gentle {
  animation: 2s ease-in-out infinite bounce-gentle;
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}

.rotate-slow {
  animation: 20s linear infinite rotate-slow;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.gradient-animate {
  background: linear-gradient(-45deg, rgb(var(--primary)), rgb(var(--secondary)), rgb(var(--accent-gold)), rgb(var(--primary)));
  background-size: 400% 400%;
  animation: 15s infinite gradient-animate;
}

@keyframes gradient-animate {
  0% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }

  100% {
    background-position: 0%;
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/