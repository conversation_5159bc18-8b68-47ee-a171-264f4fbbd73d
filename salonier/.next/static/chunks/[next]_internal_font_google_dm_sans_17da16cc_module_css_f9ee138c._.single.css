/* [next]/internal/font/google/dm_sans_17da16cc.module.css [app-client] (css) */
@font-face {
  font-family: DM Sans;
  font-style: normal;
  font-weight: 100 1000;
  font-display: swap;
  src: url("../media/rP2Yp2ywxg089UriI5_g4vlH9VoD8Cmcqbu6_K6z9mXgjU0-s.2c33b2c8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: DM Sans;
  font-style: normal;
  font-weight: 100 1000;
  font-display: swap;
  src: url("../media/rP2Yp2ywxg089UriI5_g4vlH9VoD8Cmcqbu0_K6z9mXg-s.p.40e38601.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: DM Sans Fallback;
  src: local(Arial);
  ascent-override: 94.9%;
  descent-override: 29.66%;
  line-gap-override: 0.0%;
  size-adjust: 104.53%;
}

.dm_sans_17da16cc-module__8k-zSW__className {
  font-family: DM Sans, DM Sans Fallback;
  font-style: normal;
}

.dm_sans_17da16cc-module__8k-zSW__variable {
  --font-dm-sans: "DM Sans", "DM Sans Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_dm_sans_17da16cc_module_css_f9ee138c._.single.css.map*/