# Salonier - Plataforma Inteligente para Coloristas

Una plataforma web moderna diseñada específicamente para coloristas profesionales que combina inteligencia artificial con gestión de negocio para revolucionar la industria del color capilar.

## 🎨 Características Principales

### 🤖 Análisis IA de Color
- **Captura de fotos por zonas**: Raíz, medios y puntas con validación automática
- **Análisis del color actual**: Nivel base, porcentaje de canas, porosidad
- **Fase del color deseado**: Análisis de objetivo con "Reality Check"
- **Generación de fórmulas**: Recomendaciones precisas por zona
- **Cálculo de costos**: Precio automático basado en productos utilizados
- **Predicción de viabilidad**: Advertencias sobre objetivos no alcanzables

### 👥 Gestión de Clientes
- **Perfiles completos**: Información personal, preferencias y historial
- **Seguimiento de alergias**: Sistema de alertas por severidad
- **Galería de fotos**: Antes y después de cada servicio
- **Historial detallado**: Todos los servicios realizados

### 📦 Control de Inventario
- **Gestión de productos**: Colores, reveladores y tratamientos
- **Alertas de stock bajo**: Notificaciones automáticas
- **Cálculo de costos**: En tiempo real por servicio
- **Análisis de rentabilidad**: Margen de ganancia por producto

### ⏱️ Servicios y Timers Inteligentes
- **Timers múltiples simultáneos**: Por zona y tipo de proceso
- **Alertas inteligentes**: Notificaciones a 5min, 2min y tiempo cumplido
- **Predicción de tiempo**: Estimación basada en condiciones del cabello
- **Priorización automática**: Sistema de urgencia por tipo de proceso
- **Documentación automática**: Notas de voz y fotos durante el servicio
- **Seguimiento en tiempo real**: Progreso visual con alertas críticas

### 🚨 Modo Corrección Express
- **Botón de pánico**: Acceso inmediato desde dashboard
- **Diagnóstico rápido**: Identificación automática de problemas
- **Plan de corrección**: Solución paso a paso con tiempos y costos
- **Predicción de éxito**: Probabilidad de corrección exitosa

### ⚡ Conversión Universal de Marcas
- **Base de datos masiva**: 10,000+ referencias entre 30+ marcas
- **Equivalencias exactas**: Conversión automática con nivel de confianza
- **Búsqueda inteligente**: Por código, nombre o características
- **Integración con fórmulas**: Uso directo en formulación

### 📊 Analytics y Reportes
- **Métricas de negocio**: Ingresos, servicios, ticket promedio
- **Análisis de retención**: Seguimiento de clientes
- **Servicios populares**: Ranking por rentabilidad
- **Objetivos y metas**: Seguimiento de progreso mensual

## 🚀 Tecnologías Utilizadas

- **Frontend**: Next.js 14 con App Router
- **Lenguaje**: TypeScript
- **Estilos**: Tailwind CSS
- **Componentes**: Componentes personalizados con Radix UI
- **Iconos**: Lucide React
- **Animaciones**: CSS Animations y Framer Motion
- **Estado**: React Context API
- **Datos**: Mock data (preparado para Supabase)

## 🛠️ Instalación y Configuración

### Prerrequisitos
- Node.js 18+
- npm o yarn

### Pasos de instalación

1. **Clonar el repositorio**
```bash
git clone [repository-url]
cd salonier
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Ejecutar en modo desarrollo**
```bash
npm run dev
```

4. **Abrir en el navegador**
```
http://localhost:3000
```

## 🔐 Credenciales de Demo

### Usuario Propietario
- **Email**: <EMAIL>
- **Contraseña**: demo123
- **Rol**: Propietario del salón

### Usuario Colorista
- **Email**: <EMAIL>
- **Contraseña**: demo123
- **Rol**: Colorista profesional

## 📱 Responsive Design

La aplicación está completamente optimizada para:
- **Desktop**: Experiencia completa con sidebar y múltiples columnas
- **Tablet**: Layout adaptativo con navegación optimizada
- **Mobile**: Interfaz móvil con menú hamburguesa y diseño vertical

## 🎯 Funcionalidades Implementadas

### ✅ Completado
- [x] Sistema de autenticación mock con toast notifications
- [x] Dashboard principal con métricas y **botón de pánico**
- [x] Gestión completa de clientes con perfiles detallados
- [x] **Sistema de formulación avanzado con fase del color deseado**
- [x] **Modo Corrección Express con diagnóstico IA**
- [x] **Conversión Universal de Marcas (10,000+ referencias)**
- [x] Módulo de inventario y costos con alertas
- [x] Analytics y reportes avanzados con objetivos
- [x] **Timers inteligentes con alertas y priorización**
- [x] Responsive design completo
- [x] Animaciones y transiciones suaves
- [x] **Validación IA en captura de fotos**
- [x] **Reality Check para objetivos de color**
- [x] **Sistema de alertas críticas en tiempo real**

### 🔄 Próximas Funcionalidades
- [ ] Integración con Supabase
- [ ] Análisis IA real con visión por computadora
- [ ] Sistema de citas y calendario
- [ ] Asistente de voz "Hey Salonier"
- [ ] Reportes avanzados con gráficos
- [ ] Modo offline
- [ ] Aplicación móvil nativa
- [ ] Integración con sistemas de punto de venta

## 🏗️ Arquitectura del Proyecto

```
src/
├── app/                    # App Router de Next.js
│   ├── auth/              # Páginas de autenticación
│   ├── dashboard/         # Dashboard principal
│   ├── clients/           # Gestión de clientes
│   ├── formulas/          # Sistema de formulación
│   ├── conversion/        # Conversión de marcas
│   ├── inventory/         # Control de inventario
│   ├── analytics/         # Reportes y métricas
│   └── services/          # Gestión de servicios
├── components/            # Componentes reutilizables
│   ├── ui/               # Componentes base (Button, Card, etc.)
│   ├── layout/           # Componentes de layout
│   └── features/         # Componentes específicos de funcionalidades
├── lib/                  # Utilidades y configuración
├── types/                # Definiciones de TypeScript
├── data/                 # Datos mock
└── hooks/                # Custom hooks
```

## 🎨 Sistema de Diseño

### Colores Principales
- **Primario**: Púrpura (#6B46C1)
- **Secundario**: Rosa suave (#E8B4B8)
- **Fondo**: Blanco cálido (#FAFAF9)
- **Texto**: Gris oscuro (#1A1A1A)

### Tipografía
- **Principal**: Inter (Google Fonts)
- **Display**: Playfair Display (para títulos especiales)

### Componentes
- **Botones**: Gradientes y sombras suaves
- **Cards**: Bordes redondeados y sombras sutiles
- **Inputs**: Diseño limpio con estados de focus
- **Navegación**: Sidebar colapsible y responsive

---

**Salonier** - Transformando la industria del color capilar con tecnología inteligente 🎨✨
